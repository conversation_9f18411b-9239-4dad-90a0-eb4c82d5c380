from database import db
from datetime import datetime

class AuditLog(db.Model):
    __tablename__ = 'audit_logs'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>Key('users.id'), nullable=True)
    action = db.Column(db.String(50), nullable=False)  # login, logout, create, update, delete, etc.
    resource_type = db.Column(db.String(50))  # user, customer, supplier, product, etc.
    resource_id = db.Column(db.Integer)  # ID of the affected resource
    details = db.Column(db.Text)  # Detailed description of the action
    ip_address = db.Column(db.String(45))  # IPv4 or IPv6
    user_agent = db.Column(db.String(255))  # Browser/client information
    old_values = db.Column(db.Text)  # JSON string of old values (for updates)
    new_values = db.Column(db.Text)  # JSON string of new values (for updates)
    status = db.Column(db.String(20), default='success')  # success, failed, error
    error_message = db.Column(db.Text)  # Error details if status is failed/error
    branch_id = db.Column(db.Integer, db.ForeignKey('branches.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    
    # Relationships
    branch = db.relationship('Branch', backref='audit_logs')
    
    @staticmethod
    def log_action(user_id, action, resource_type=None, resource_id=None, details=None, 
                   ip_address=None, user_agent=None, old_values=None, new_values=None, 
                   status='success', error_message=None, branch_id=None):
        """Create a new audit log entry"""
        audit_log = AuditLog(
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            details=details,
            ip_address=ip_address,
            user_agent=user_agent,
            old_values=old_values,
            new_values=new_values,
            status=status,
            error_message=error_message,
            branch_id=branch_id
        )
        db.session.add(audit_log)
        db.session.commit()
        return audit_log
    
    def __repr__(self):
        return f'<AuditLog {self.action} by User {self.user_id}>'
