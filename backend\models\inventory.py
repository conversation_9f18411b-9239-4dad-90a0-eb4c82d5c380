from database import db
from datetime import datetime

class Inventory(db.Model):
    __tablename__ = 'inventory'
    
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.<PERSON><PERSON>ger, db.<PERSON>ey('products.id'), nullable=False)
    quantity = db.Column(db.Integer, default=0)
    branch_id = db.Column(db.Integer, db.ForeignKey('branches.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    branch = db.relationship('Branch', backref='inventory_items')
    product = db.relationship('Product', backref='inventory')
    
    def __repr__(self):
        return f'<Inventory Product {self.product_id}: {self.quantity}>'
