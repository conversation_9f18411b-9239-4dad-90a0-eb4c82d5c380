#!/usr/bin/env python3
"""
Script to create default admin user
"""
from app import create_app
from database import db
from models.user import User
from werkzeug.security import generate_password_hash

def create_admin_user():
    app = create_app()
    
    with app.app_context():
        # Check if admin user already exists
        admin_user = User.query.filter_by(username='admin').first()
        
        if admin_user:
            print("Admin user already exists!")
            print(f"Username: {admin_user.username}")
            print(f"Email: {admin_user.email}")
            return
        
        # Create admin user
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            password_hash=generate_password_hash('admin123'),
            first_name='System',
            last_name='Administrator',
            role='admin',
            is_active=True,
            is_verified=True
        )
        
        db.session.add(admin_user)
        db.session.commit()
        
        print("✅ Admin user created successfully!")
        print("Username: admin")
        print("Password: admin123")
        print("Email: <EMAIL>")

if __name__ == '__main__':
    create_admin_user()
