#!/usr/bin/env python3
import requests
import json

def test_login():
    url = "http://localhost:5000/api/auth/login"
    data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Login successful!")
            print(f"User: {result.get('user', {}).get('username')}")
            print(f"Role: {result.get('user', {}).get('role')}")
            print(f"Token: {result.get('access_token', 'N/A')[:50]}...")
        else:
            print("❌ Login failed!")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_login()
