"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
exports.getListItemTextUtilityClass = getListItemTextUtilityClass;
var _generateUtilityClasses = _interopRequireDefault(require("@mui/utils/generateUtilityClasses"));
var _generateUtilityClass = _interopRequireDefault(require("@mui/utils/generateUtilityClass"));
function getListItemTextUtilityClass(slot) {
  return (0, _generateUtilityClass.default)('MuiListItemText', slot);
}
const listItemTextClasses = (0, _generateUtilityClasses.default)('MuiListItemText', ['root', 'multiline', 'dense', 'inset', 'primary', 'secondary']);
var _default = exports.default = listItemTextClasses;