from database import db
from datetime import datetime

class Account(db.Model):
    __tablename__ = 'accounts'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    account_code = db.Column(db.String(20), unique=True, nullable=False)
    account_name = db.Column(db.String(100), nullable=False)
    account_name_ar = db.Column(db.String(100))
    account_type = db.Column(db.String(20), nullable=False)  # asset, liability, equity, revenue, expense
    account_category = db.Column(db.String(50))  # current_asset, fixed_asset, etc.
    parent_account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))
    is_active = db.Column(db.Boolean, default=True)
    is_system_account = db.Column(db.Boolean, default=False)
    opening_balance = db.Column(db.Numeric(15, 2), default=0)
    current_balance = db.Column(db.Numeric(15, 2), default=0)
    branch_id = db.Column(db.Integer, db.ForeignKey('branches.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    branch = db.relationship('Branch', backref='accounts')
    parent_account = db.relationship('Account', remote_side=[id], backref='sub_accounts')
    
    def __repr__(self):
        return f'<Account {self.account_code}: {self.account_name}>'
