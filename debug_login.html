<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار تسجيل الدخول</h1>
        <p>هذا الملف لاختبار الاتصال مع الخادم وتشخيص مشاكل تسجيل الدخول</p>
        <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
            <strong>حالة الخوادم:</strong><br>
            ✅ الخادم الخلفي: يعمل على المنفذ 5000<br>
            ✅ الخادم الأمامي: يعمل على المنفذ 3000<br>
            ✅ المستخدم الافتراضي: admin / admin123
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>

            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" value="admin123" required>
            </div>

            <button type="submit">تسجيل الدخول</button>
        </form>

        <div id="result"></div>

        <hr style="margin: 30px 0;">

        <h3>اختبارات إضافية:</h3>
        <button onclick="testServerConnection()">اختبار الاتصال بالخادم</button>
        <button onclick="testCORS()">اختبار CORS</button>
        <button onclick="checkLocalStorage()">فحص التخزين المحلي</button>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';

        // تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');

            resultDiv.innerHTML = '<div class="info">جاري تسجيل الدخول...</div>';

            try {
                console.log('محاولة تسجيل الدخول:', { username, password });

                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                console.log('استجابة الخادم:', response);

                const data = await response.json();
                console.log('بيانات الاستجابة:', data);

                if (response.ok) {
                    // حفظ التوكن
                    if (data.access_token) {
                        localStorage.setItem('access_token', data.access_token);
                        localStorage.setItem('refresh_token', data.refresh_token);
                    }

                    resultDiv.innerHTML = `
                        <div class="success">
                            تم تسجيل الدخول بنجاح!

                            المستخدم: ${data.user?.username || 'غير محدد'}
                            الدور: ${data.user?.role || 'غير محدد'}
                            التوكن: ${data.access_token ? 'تم الحصول عليه' : 'غير موجود'}

                            الآن يجب أن تنتقل إلى الصفحة الرئيسية...
                        </div>
                    `;

                    // محاولة الانتقال إلى الصفحة الرئيسية
                    setTimeout(() => {
                        window.location.href = 'http://localhost:3000/dashboard';
                    }, 2000);

                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            خطأ في تسجيل الدخول:
                            ${data.error || 'خطأ غير معروف'}

                            كود الحالة: ${response.status}
                        </div>
                    `;
                }

            } catch (error) {
                console.error('خطأ في الشبكة:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        خطأ في الاتصال بالخادم:
                        ${error.message}

                        تأكد من أن الخادم يعمل على المنفذ 5000
                    </div>
                `;
            }
        });

        // اختبار الاتصال بالخادم
        async function testServerConnection() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">اختبار الاتصال بالخادم...</div>';

            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            الخادم يعمل بشكل طبيعي!
                            الحالة: ${data.status}
                            الرسالة: ${data.message}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            الخادم لا يستجيب بشكل صحيح
                            كود الحالة: ${response.status}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        لا يمكن الاتصال بالخادم:
                        ${error.message}
                    </div>
                `;
            }
        }

        // اختبار CORS
        async function testCORS() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">اختبار CORS...</div>';

            try {
                const response = await fetch(`${API_BASE}/health`, {
                    method: 'OPTIONS'
                });

                resultDiv.innerHTML = `
                    <div class="success">
                        CORS يعمل بشكل طبيعي
                        Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        مشكلة في CORS:
                        ${error.message}
                    </div>
                `;
            }
        }

        // فحص التخزين المحلي
        function checkLocalStorage() {
            const resultDiv = document.getElementById('result');

            const accessToken = localStorage.getItem('access_token');
            const refreshToken = localStorage.getItem('refresh_token');

            resultDiv.innerHTML = `
                <div class="info">
                    حالة التخزين المحلي:

                    Access Token: ${accessToken ? 'موجود' : 'غير موجود'}
                    Refresh Token: ${refreshToken ? 'موجود' : 'غير موجود'}

                    ${accessToken ? `Access Token: ${accessToken.substring(0, 50)}...` : ''}
                </div>
            `;
        }

        // فحص التخزين المحلي عند تحميل الصفحة
        window.addEventListener('load', () => {
            checkLocalStorage();
        });
    </script>
</body>
</html>
